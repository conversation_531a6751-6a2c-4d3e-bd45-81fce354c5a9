#!/usr/bin/env python3
"""
Script to process LLukas22/fiqa dataset from Hugging Face:
1. Load the dataset from Hugging Face
2. Select up to specified number of samples
3. Map 'question' column to 'prompt' and 'answer' column to 'response'
4. Split into train/test sets (90%/10% by default)
5. Save as separate JSON files in the specified folder

The dataset structure:
- question: The financial question
- answer: The corresponding answer
"""

import json
import os
from datasets import load_dataset
import argparse
from typing import Tuple
import time
import random


def split_train_test(data: list, test_ratio: float = 0.1, seed: int = 42) -> Tuple[list, list]:
    """
    Split data into train and test sets
    
    Args:
        data (list): List of data samples
        test_ratio (float): Ratio of data to use for testing (default: 0.1 for 10%)
        seed (int): Random seed for reproducibility
    
    Returns:
        Tuple[list, list]: (train_data, test_data)
    """
    # Set random seed for reproducibility
    random.seed(seed)
    
    # Shuffle the data
    shuffled_data = data.copy()
    random.shuffle(shuffled_data)
    
    # Calculate split index
    test_size = int(len(shuffled_data) * test_ratio)
    train_size = len(shuffled_data) - test_size
    
    # Split the data
    train_data = shuffled_data[:train_size]
    test_data = shuffled_data[train_size:]
    
    print(f"Data split: {len(train_data)} training samples, {len(test_data)} test samples")
    
    return train_data, test_data


def load_fiqa_dataset(max_samples: int = 10000, split: str = 'train'):
    """
    Load the LLukas22/fiqa dataset from Hugging Face
    
    Args:
        max_samples (int): Maximum number of samples to keep
        split (str): Dataset split to load ('train', 'test', etc.)
    
    Returns:
        Dataset: The loaded dataset
    """
    print(f"Loading LLukas22/fiqa dataset...")
    start_time = time.time()
    
    # Load dataset
    dataset = load_dataset('LLukas22/fiqa')
    
    # Get the specified split
    data = dataset[split]
    
    # Limit to max_samples early to save memory
    if len(data) > max_samples:
        print(f"Dataset has {len(data)} samples, selecting first {max_samples}")
        data = data.shuffle(seed=42)  # Shuffle for randomness
        data = data.select(range(max_samples))
    else:
        print(f"Dataset has {len(data)} samples")
    
    load_time = time.time() - start_time
    print(f"Loading completed in {load_time:.2f} seconds")
    
    return data


def process_fiqa_dataset(dataset, output_dir: str, enable_split: bool = True, test_ratio: float = 0.1):
    """
    Process the FIQA dataset and save as JSON with train/test split
    
    Args:
        dataset: The loaded dataset
        output_dir (str): Directory to save processed files
        enable_split (bool): Whether to split data into train/test sets
        test_ratio (float): Ratio of data to use for testing (default: 0.1 for 10%)
    """
    print(f"Processing FIQA dataset...")
    start_time = time.time()
    
    # Process data in batches for better memory efficiency
    batch_size = 1000
    processed_data = []
    
    # Process in batches
    total_samples = len(dataset)
    for i in range(0, total_samples, batch_size):
        end_idx = min(i + batch_size, total_samples)
        batch = dataset[i:end_idx]
        
        # Process batch - map question to prompt and answer to response
        for j in range(len(batch['question'])):
            processed_data.append({
                'prompt': batch['question'][j],
                'response': batch['answer'][j]
            })
        
        # Show progress
        if i % (batch_size * 5) == 0:  # Every 5 batches
            print(f"  Processed {min(end_idx, total_samples)}/{total_samples} samples...")
    
    # Split data into train/test if enabled
    if enable_split:
        train_data, test_data = split_train_test(processed_data, test_ratio=test_ratio)
        
        # Save train data
        train_filename = os.path.join(output_dir, "fiqa_train.json")
        print(f"Saving training data to {train_filename}...")
        with open(train_filename, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, indent=2, ensure_ascii=False)
        
        # Save test data
        test_filename = os.path.join(output_dir, "fiqa_test.json")
        print(f"Saving test data to {test_filename}...")
        with open(test_filename, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        process_time = time.time() - start_time
        print(f"Saved {len(train_data)} training samples to: {train_filename}")
        print(f"Saved {len(test_data)} test samples to: {test_filename}")
        print(f"Processing completed in {process_time:.2f} seconds")
    else:
        # Save as single JSON file (no split)
        filename = os.path.join(output_dir, "fiqa.json")
        print(f"Saving to {filename}...")
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, indent=2, ensure_ascii=False)
        
        process_time = time.time() - start_time
        print(f"Saved {len(processed_data)} samples to: {filename}")
        print(f"Processing completed in {process_time:.2f} seconds")


def main():
    parser = argparse.ArgumentParser(description='Process LLukas22/fiqa dataset from Hugging Face')
    parser.add_argument('--output-dir', default='LLukas22_fiqa', 
                       help='Output directory for processed files')
    parser.add_argument('--max-samples', type=int, default=10000,
                       help='Maximum number of samples to process')
    parser.add_argument('--split', default='train',
                       help='Dataset split to process')
    parser.add_argument('--enable-split', action='store_true', default=True,
                       help='Enable train-test split (default: True)')
    parser.add_argument('--no-split', action='store_true',
                       help='Disable train-test split (save as single file)')
    parser.add_argument('--test-ratio', type=float, default=0.1,
                       help='Ratio of data to use for testing (default: 0.1 for 10%%)')
    
    args = parser.parse_args()
    
    # Handle split arguments
    if args.no_split:
        args.enable_split = False
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"Processing LLukas22/fiqa dataset")
    print(f"Max samples: {args.max_samples}")
    print(f"Output directory: {args.output_dir}")
    print(f"Train-test split: {'Enabled' if args.enable_split else 'Disabled'}")
    if args.enable_split:
        print(f"Test ratio: {args.test_ratio:.1%}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # Load dataset
        dataset = load_fiqa_dataset(max_samples=args.max_samples, split=args.split)
        
        # Process and save
        process_fiqa_dataset(dataset, args.output_dir, enable_split=args.enable_split, test_ratio=args.test_ratio)
        
    except Exception as e:
        print(f"Error processing dataset: {e}")
        return
    
    total_time = time.time() - start_time
    print("-" * 50)
    print(f"Total processing time: {total_time:.2f} seconds")
    print(f"Processing complete! Files saved to: {args.output_dir}")
    
    # List generated files
    if os.path.exists(args.output_dir):
        print(f"\nGenerated files:")
        files = sorted(os.listdir(args.output_dir))
        for file in files:
            file_path = os.path.join(args.output_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  {file} ({size:,} bytes)")


if __name__ == "__main__":
    main()
