#!/usr/bin/env python3
"""
Optimized script to process CAMEL-AI datasets for physics, chemistry, and biology:
1. Load each dataset from Hugging Face with optimizations
2. Select up to 10k samples from each efficiently
3. Keep only message_1 (as prompt), message_2 (as response), and topic
4. Save each category as separate JSON files in a separate folder

Performance optimizations:
- Streaming dataset loading for faster initial access
- Direct processing without pandas conversion
- Batch processing for better memory usage
- Optional parallel processing
"""

import json
import os
from datasets import load_dataset
import argparse
from typing import Dict, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import random


def split_train_test(data: list, test_ratio: float = 0.1, seed: int = 42) -> Tuple[list, list]:
    """
    Split data into train and test sets

    Args:
        data (list): List of data samples
        test_ratio (float): Ratio of data to use for testing (default: 0.1 for 10%)
        seed (int): Random seed for reproducibility

    Returns:
        Tuple[list, list]: (train_data, test_data)
    """
    # Set random seed for reproducibility
    random.seed(seed)

    # Shuffle the data
    shuffled_data = data.copy()
    random.shuffle(shuffled_data)

    # Calculate split index
    test_size = int(len(shuffled_data) * test_ratio)
    train_size = len(shuffled_data) - test_size

    # Split the data
    train_data = shuffled_data[:train_size]
    test_data = shuffled_data[train_size:]

    print(f"Data split: {len(train_data)} training samples, {len(test_data)} test samples")

    return train_data, test_data


def load_camel_dataset_optimized(dataset_name: str, max_samples: int = 10000, split='test') -> Dict:
    """
    Load a CAMEL-AI dataset from Hugging Face with optimizations
    
    Args:
        dataset_name (str): Name of the dataset (e.g., 'camel-ai/biology')
        max_samples (int): Maximum number of samples to keep
    
    Returns:
        Dict: The loaded dataset
    """
    print(f"Loading {dataset_name} dataset...")
    start_time = time.time()
    
    # Load dataset - we can use streaming for very large datasets
    # but for CAMEL datasets (20k samples), regular loading is fine
    dataset = load_dataset(dataset_name)
    
    # Get the train split
    train_data = dataset[split]
    
    # Limit to max_samples early to save memory
    if len(train_data) > max_samples:
        print(f"Dataset has {len(train_data)} samples, selecting first {max_samples}")
        train_data = train_data.shuffle(seed=42) # Shuffle for randomness
        train_data = train_data.select(range(max_samples))
    else:
        print(f"Dataset has {len(train_data)} samples")
    
    load_time = time.time() - start_time
    print(f"Loading completed in {load_time:.2f} seconds")
    
    return train_data


def process_camel_dataset_optimized(dataset, category_name: str, output_dir: str, enable_split: bool = True, test_ratio: float = 0.1):
    """
    Process a CAMEL dataset and save as JSON with optimizations

    Args:
        dataset: The loaded dataset
        category_name (str): Name of the category (e.g., 'biology', 'physics', 'chemistry')
        output_dir (str): Directory to save processed files
        enable_split (bool): Whether to split data into train/test sets
        test_ratio (float): Ratio of data to use for testing (default: 0.1 for 10%)
    """
    print(f"Processing {category_name} dataset...")
    start_time = time.time()
    
    # Process data in batches for better memory efficiency
    batch_size = 1000
    processed_data = []
    topics_count = {}
    
    # Process in batches
    total_samples = len(dataset)
    for i in range(0, total_samples, batch_size):
        end_idx = min(i + batch_size, total_samples)
        batch = dataset[i:end_idx]
        
        # Process batch
        for j in range(len(batch['message_1'])):
            processed_data.append({
                'prompt': batch['message_1'][j],
                'response': batch['message_2'][j],
                'topic': batch['topic;'][j]
            })
            
            # Count topics for statistics
            topic = batch['topic;'][j]
            topics_count[topic] = topics_count.get(topic, 0) + 1
        
        # Show progress
        if i % (batch_size * 5) == 0:  # Every 5 batches
            print(f"  Processed {min(end_idx, total_samples)}/{total_samples} samples...")
    
    # Split data into train/test if enabled
    if enable_split:
        train_data, test_data = split_train_test(processed_data, test_ratio=test_ratio)

        # Save train data
        train_filename = os.path.join(output_dir, f"{category_name}_train.json")
        print(f"Saving training data to {train_filename}...")
        with open(train_filename, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, indent=2, ensure_ascii=False)

        # Save test data
        test_filename = os.path.join(output_dir, f"{category_name}_test.json")
        print(f"Saving test data to {test_filename}...")
        with open(test_filename, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)

        process_time = time.time() - start_time
        print(f"Saved {len(train_data)} training samples to: {train_filename}")
        print(f"Saved {len(test_data)} test samples to: {test_filename}")
        print(f"Processing completed in {process_time:.2f} seconds")
    else:
        # Save as single JSON file (original behavior)
        filename = os.path.join(output_dir, f"{category_name}.json")
        print(f"Saving to {filename}...")

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, indent=2, ensure_ascii=False)

        process_time = time.time() - start_time
        print(f"Saved {len(processed_data)} samples to: {filename}")
        print(f"Processing completed in {process_time:.2f} seconds")
    
    # Print some statistics
    sorted_topics = sorted(topics_count.items(), key=lambda x: x[1], reverse=True)
    print(f"Topics in {category_name}:")
    for topic, count in sorted_topics[:10]:
        print(f"  {topic}: {count} samples")
    if len(sorted_topics) > 10:
        print(f"  ... and {len(sorted_topics) - 10} more topics")


def process_single_category(args_tuple):
    """
    Process a single category - used for parallel processing

    Args:
        args_tuple: Tuple of (category, dataset_mapping, max_samples, output_dir, split, enable_split, test_ratio)

    Returns:
        Tuple of (category, success, error_message)
    """
    category, dataset_mapping, max_samples, output_dir, split, enable_split, test_ratio = args_tuple

    try:
        # Load dataset
        dataset = load_camel_dataset_optimized(
            dataset_mapping[category],
            max_samples=max_samples, split=split
        )

        # Process and save
        process_camel_dataset_optimized(dataset, category, output_dir, enable_split=enable_split, test_ratio=test_ratio)

        return category, True, None

    except Exception as e:
        return category, False, str(e)


def main():
    parser = argparse.ArgumentParser(description='Process CAMEL-AI datasets (optimized version)')
    parser.add_argument('--output-dir', default='camel_datasets_processed', 
                       help='Output directory for processed files')
    parser.add_argument('--max-samples', type=int, default=10000,
                       help='Maximum number of samples per dataset')
    parser.add_argument('--categories', nargs='+', 
                       default=['biology', 'physics', 'chemistry'],
                       help='Categories to process')
    parser.add_argument('--parallel', action='store_true',
                       help='Process categories in parallel (faster but uses more memory)')
    parser.add_argument('--max-workers', type=int, default=3,
                       help='Maximum number of parallel workers (only used with --parallel)')
    parser.add_argument('--split', default='train',
                       help='Dataset split to process')
    parser.add_argument('--enable-split', action='store_true', default=True,
                       help='Enable train-test split (default: True)')
    parser.add_argument('--no-split', action='store_true',
                       help='Disable train-test split (save as single file)')
    parser.add_argument('--test-ratio', type=float, default=0.1,
                       help='Ratio of data to use for testing (default: 0.1 for 10%%)')

    args = parser.parse_args()

    # Handle split arguments
    if args.no_split:
        args.enable_split = False
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Dataset mapping
    dataset_mapping = {
        'biology': 'camel-ai/biology',
        'physics': 'camel-ai/physics', 
        'chemistry': 'camel-ai/chemistry'
    }
    
    # Filter valid categories
    valid_categories = [cat for cat in args.categories if cat in dataset_mapping]
    invalid_categories = [cat for cat in args.categories if cat not in dataset_mapping]
    
    if invalid_categories:
        print(f"Warning: Unknown categories {invalid_categories}. Skipping...")
    
    if not valid_categories:
        print("No valid categories to process!")
        return
    
    print(f"Processing {len(valid_categories)} categories: {valid_categories}")
    print(f"Max samples per dataset: {args.max_samples}")
    print(f"Output directory: {args.output_dir}")
    print(f"Parallel processing: {'Enabled' if args.parallel else 'Disabled'}")
    print(f"Train-test split: {'Enabled' if args.enable_split else 'Disabled'}")
    if args.enable_split:
        print(f"Test ratio: {args.test_ratio:.1%}")
    print("-" * 50)
    
    start_time = time.time()
    
    if args.parallel and len(valid_categories) > 1:
        # Parallel processing
        print("Using parallel processing...")
        
        # Prepare arguments for parallel processing
        process_args = [
            (category, dataset_mapping, args.max_samples, args.output_dir, args.split, args.enable_split, args.test_ratio)
            for category in valid_categories
        ]
        
        # Process in parallel
        with ThreadPoolExecutor(max_workers=min(args.max_workers, len(valid_categories))) as executor:
            # Submit all tasks
            future_to_category = {
                executor.submit(process_single_category, args): args[0] 
                for args in process_args
            }
            
            # Collect results
            for future in as_completed(future_to_category):
                category, success, error = future.result()
                if not success:
                    print(f"Error processing {category}: {error}")
    
    else:
        # Sequential processing
        print("Using sequential processing...")
        
        for category in valid_categories:
            try:
                # Load dataset
                dataset = load_camel_dataset_optimized(
                    dataset_mapping[category], 
                    max_samples=args.max_samples, split=args.split
                )
                
                # Process and save
                process_camel_dataset_optimized(dataset, category, args.output_dir, enable_split=args.enable_split, test_ratio=args.test_ratio)
                
            except Exception as e:
                print(f"Error processing {category}: {e}")
                continue
    
    total_time = time.time() - start_time
    print("-" * 50)
    print(f"Total processing time: {total_time:.2f} seconds")
    print(f"Processing complete! Files saved to: {args.output_dir}")
    
    # List generated files
    if os.path.exists(args.output_dir):
        print(f"\nGenerated files:")
        files = sorted(os.listdir(args.output_dir))
        for file in files:
            file_path = os.path.join(args.output_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  {file} ({size:,} bytes)")


if __name__ == "__main__":
    main()
