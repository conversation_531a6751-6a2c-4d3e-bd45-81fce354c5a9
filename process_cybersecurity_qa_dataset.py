#!/usr/bin/env python3
"""
Script to process Cybersecurity QA dataset from Kaggle:
1. Download the dataset from Kaggle using kaggle API
2. Load and process the CSV file
3. Map question/answer columns to 'prompt' and 'response'
4. Split into train/test sets (90%/10% by default)
5. Save as separate JSON files in the specified folder

Requirements:
- kaggle package: pip install kaggle
- Kaggle API credentials configured (~/.kaggle/kaggle.json)

Dataset: https://www.kaggle.com/datasets/zobayer0x01/cybersecurity-qa
"""

import json
import os
import pandas as pd
import argparse
from typing import Tuple
import time
import random
import subprocess
import sys


def install_kaggle():
    """Install kaggle package if not available"""
    try:
        import kaggle
        return True
    except ImportError:
        print("Kaggle package not found. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "kaggle"])
            import kaggle
            return True
        except Exception as e:
            print(f"Failed to install kaggle package: {e}")
            print("Please install manually: pip install kaggle")
            return False


def split_train_test(data: list, test_ratio: float = 0.1, seed: int = 42) -> Tuple[list, list]:
    """
    Split data into train and test sets
    
    Args:
        data (list): List of data samples
        test_ratio (float): Ratio of data to use for testing (default: 0.1 for 10%)
        seed (int): Random seed for reproducibility
    
    Returns:
        Tuple[list, list]: (train_data, test_data)
    """
    # Set random seed for reproducibility
    random.seed(seed)
    
    # Shuffle the data
    shuffled_data = data.copy()
    random.shuffle(shuffled_data)
    
    # Calculate split index
    test_size = int(len(shuffled_data) * test_ratio)
    train_size = len(shuffled_data) - test_size
    
    # Split the data
    train_data = shuffled_data[:train_size]
    test_data = shuffled_data[train_size:]
    
    print(f"Data split: {len(train_data)} training samples, {len(test_data)} test samples")
    
    return train_data, test_data


def download_kaggle_dataset(dataset_name: str, download_path: str = "./"):
    """
    Download dataset from Kaggle
    
    Args:
        dataset_name (str): Kaggle dataset identifier (e.g., 'zobayer0x01/cybersecurity-qa')
        download_path (str): Path to download the dataset
    
    Returns:
        bool: True if successful, False otherwise
    """
    if not install_kaggle():
        return False
    
    try:
        import kaggle
        print(f"Downloading dataset: {dataset_name}")
        
        # Create download directory if it doesn't exist
        os.makedirs(download_path, exist_ok=True)
        
        # Download and unzip dataset
        kaggle.api.dataset_download_files(
            dataset_name, 
            path=download_path, 
            unzip=True
        )
        
        print(f"Dataset downloaded to: {download_path}")
        return True
        
    except Exception as e:
        print(f"Error downloading dataset: {e}")
        print("Make sure you have:")
        print("1. Kaggle API credentials configured (~/.kaggle/kaggle.json)")
        print("2. Accepted the dataset terms on Kaggle website")
        return False


def find_csv_files(directory: str):
    """Find CSV files in the directory"""
    csv_files = []
    for file in os.listdir(directory):
        if file.endswith('.csv'):
            csv_files.append(os.path.join(directory, file))
    return csv_files


def load_cybersecurity_dataset(csv_path: str, max_samples: int = None):
    """
    Load the cybersecurity QA dataset from CSV
    
    Args:
        csv_path (str): Path to the CSV file
        max_samples (int): Maximum number of samples to keep
    
    Returns:
        pd.DataFrame: The loaded dataset
    """
    print(f"Loading dataset from: {csv_path}")
    start_time = time.time()
    
    # Load CSV file
    df = pd.read_csv(csv_path)
    
    print(f"Dataset loaded with {len(df)} samples")
    print(f"Columns: {list(df.columns)}")
    
    # Limit to max_samples if specified
    if max_samples and len(df) > max_samples:
        print(f"Selecting first {max_samples} samples")
        df = df.sample(n=max_samples, random_state=42).reset_index(drop=True)
    
    load_time = time.time() - start_time
    print(f"Loading completed in {load_time:.2f} seconds")
    
    return df


def process_cybersecurity_dataset(df: pd.DataFrame, output_dir: str, enable_split: bool = True, test_ratio: float = 0.1):
    """
    Process the cybersecurity dataset and save as JSON with train/test split
    
    Args:
        df (pd.DataFrame): The loaded dataset
        output_dir (str): Directory to save processed files
        enable_split (bool): Whether to split data into train/test sets
        test_ratio (float): Ratio of data to use for testing (default: 0.1 for 10%)
    """
    print(f"Processing cybersecurity QA dataset...")
    start_time = time.time()
    
    # Detect question and answer columns
    question_col = None
    answer_col = None
    
    # Common column names for questions
    question_names = ['question', 'Question', 'query', 'Query', 'prompt', 'Prompt', 'q', 'Q']
    answer_names = ['answer', 'Answer', 'response', 'Response', 'reply', 'Reply', 'a', 'A']
    
    for col in df.columns:
        if col in question_names or 'question' in col.lower():
            question_col = col
        elif col in answer_names or 'answer' in col.lower():
            answer_col = col
    
    if not question_col or not answer_col:
        print("Available columns:", list(df.columns))
        print("Please specify the correct column names manually in the script")
        # Try first two columns as fallback
        if len(df.columns) >= 2:
            question_col = df.columns[0]
            answer_col = df.columns[1]
            print(f"Using columns: '{question_col}' as question, '{answer_col}' as answer")
        else:
            raise ValueError("Could not identify question and answer columns")
    
    print(f"Using columns: '{question_col}' -> prompt, '{answer_col}' -> response")
    
    # Process data
    processed_data = []
    
    for idx, row in df.iterrows():
        # Skip rows with missing data
        if pd.isna(row[question_col]) or pd.isna(row[answer_col]):
            continue
            
        processed_data.append({
            'prompt': str(row[question_col]).strip(),
            'response': str(row[answer_col]).strip()
        })
        
        # Show progress
        if (idx + 1) % 1000 == 0:
            print(f"  Processed {idx + 1}/{len(df)} samples...")
    
    print(f"Processed {len(processed_data)} valid samples")
    
    # Split data into train/test if enabled
    if enable_split:
        train_data, test_data = split_train_test(processed_data, test_ratio=test_ratio)
        
        # Save train data
        train_filename = os.path.join(output_dir, "cybersecurity_qa_train.json")
        print(f"Saving training data to {train_filename}...")
        with open(train_filename, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, indent=2, ensure_ascii=False)
        
        # Save test data
        test_filename = os.path.join(output_dir, "cybersecurity_qa_test.json")
        print(f"Saving test data to {test_filename}...")
        with open(test_filename, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        process_time = time.time() - start_time
        print(f"Saved {len(train_data)} training samples to: {train_filename}")
        print(f"Saved {len(test_data)} test samples to: {test_filename}")
        print(f"Processing completed in {process_time:.2f} seconds")
    else:
        # Save as single JSON file (no split)
        filename = os.path.join(output_dir, "cybersecurity_qa.json")
        print(f"Saving to {filename}...")
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, indent=2, ensure_ascii=False)
        
        process_time = time.time() - start_time
        print(f"Saved {len(processed_data)} samples to: {filename}")
        print(f"Processing completed in {process_time:.2f} seconds")


def main():
    parser = argparse.ArgumentParser(description='Process Cybersecurity QA dataset from Kaggle')
    parser.add_argument('--output-dir', default='cybersecurity_qa', 
                       help='Output directory for processed files')
    parser.add_argument('--max-samples', type=int, default=10000,
                       help='Maximum number of samples to process')
    parser.add_argument('--download-path', default='./kaggle_data',
                       help='Path to download the dataset')
    parser.add_argument('--csv-file', default=None,
                       help='Path to CSV file (skip download if provided)')
    parser.add_argument('--enable-split', action='store_true', default=True,
                       help='Enable train-test split (default: True)')
    parser.add_argument('--no-split', action='store_true',
                       help='Disable train-test split (save as single file)')
    parser.add_argument('--test-ratio', type=float, default=0.1,
                       help='Ratio of data to use for testing (default: 0.1 for 10%%)')
    
    args = parser.parse_args()
    
    # Handle split arguments
    if args.no_split:
        args.enable_split = False
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"Processing Cybersecurity QA dataset")
    print(f"Output directory: {args.output_dir}")
    print(f"Train-test split: {'Enabled' if args.enable_split else 'Disabled'}")
    if args.enable_split:
        print(f"Test ratio: {args.test_ratio:.1%}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        csv_file = args.csv_file
        
        if not csv_file:
            # Download dataset from Kaggle
            if not download_kaggle_dataset('zobayer0x01/cybersecurity-qa', args.download_path):
                print("Failed to download dataset. Exiting.")
                return
            
            # Find CSV files in download directory
            csv_files = find_csv_files(args.download_path)
            if not csv_files:
                print(f"No CSV files found in {args.download_path}")
                return
            
            csv_file = csv_files[0]  # Use first CSV file found
            print(f"Using CSV file: {csv_file}")
        
        # Load dataset
        df = load_cybersecurity_dataset(csv_file, max_samples=args.max_samples)
        
        # Process and save
        process_cybersecurity_dataset(df, args.output_dir, enable_split=args.enable_split, test_ratio=args.test_ratio)
        
    except Exception as e:
        print(f"Error processing dataset: {e}")
        return
    
    total_time = time.time() - start_time
    print("-" * 50)
    print(f"Total processing time: {total_time:.2f} seconds")
    print(f"Processing complete! Files saved to: {args.output_dir}")
    
    # List generated files
    if os.path.exists(args.output_dir):
        print(f"\nGenerated files:")
        files = sorted(os.listdir(args.output_dir))
        for file in files:
            file_path = os.path.join(args.output_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  {file} ({size:,} bytes)")


if __name__ == "__main__":
    main()
